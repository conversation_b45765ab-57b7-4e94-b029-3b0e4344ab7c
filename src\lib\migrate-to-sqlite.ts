import { promises as fs } from 'fs';
import path from 'path';
import { initDatabase } from './database';
import { UserModel } from './models/user';
import { TemplateModel } from './models/template';
import { Template } from '@/types/template';

// Migration script to move data from JSON files to SQLite
export async function migrateToSQLite(): Promise<{
  success: boolean;
  message: string;
  details: {
    usersmigrated: number;
    templatesMigrated: number;
    errors: string[];
  };
}> {
  const errors: string[] = [];
  let usersCount = 0;
  let templatesCount = 0;

  try {
    // Initialize database
    await initDatabase();
    console.log('Database initialized successfully');

    // Migrate users from users.json
    try {
      const usersFilePath = path.join(process.cwd(), 'data', 'users.json');
      
      try {
        await fs.access(usersFilePath);
        const usersData = await fs.readFile(usersFilePath, 'utf-8');
        
        // Try to decrypt the data (it might be encrypted)
        let users: any[] = [];
        try {
          // First try to parse as plain JSON
          users = JSON.parse(usersData);
        } catch {
          // If that fails, try to decrypt it
          try {
            const { decrypt } = await import('./models/user');
            const decryptedData = decrypt(usersData);
            users = JSON.parse(decryptedData);
          } catch (decryptError) {
            errors.push(`Failed to decrypt users data: ${decryptError}`);
          }
        }

        // Migrate each user
        for (const user of users) {
          try {
            // Check if user already exists
            const existingUser = await UserModel.findByUsername(user.username);
            if (!existingUser) {
              await UserModel.create({
                username: user.username,
                passwordHash: user.passwordHash,
                recoveryOptions: user.recoveryOptions || {}
              });
              usersCount++;
              console.log(`Migrated user: ${user.username}`);
            } else {
              console.log(`User already exists: ${user.username}`);
            }
          } catch (userError) {
            errors.push(`Failed to migrate user ${user.username}: ${userError}`);
          }
        }
      } catch (fileError) {
        console.log('No users.json file found or file is empty');
      }
    } catch (usersError) {
      errors.push(`Error migrating users: ${usersError}`);
    }

    // Migrate templates from templates.json
    try {
      const templatesFilePath = path.join(process.cwd(), 'public', 'templates', 'templates.json');
      
      try {
        await fs.access(templatesFilePath);
        const templatesData = await fs.readFile(templatesFilePath, 'utf-8');
        const templates: Template[] = JSON.parse(templatesData);

        // Migrate each template
        for (const template of templates) {
          try {
            // Check if template already exists
            const existingTemplate = await TemplateModel.findById(template.id);
            if (!existingTemplate) {
              await TemplateModel.create({
                id: template.id,
                name: template.name,
                description: template.description,
                filename: template.filename,
                placeholders: template.placeholders,
                layoutSize: template.layoutSize,
                hasApplicantPhoto: template.hasApplicantPhoto || false
              });
              templatesCount++;
              console.log(`Migrated template: ${template.name}`);
            } else {
              console.log(`Template already exists: ${template.name}`);
            }
          } catch (templateError) {
            errors.push(`Failed to migrate template ${template.name}: ${templateError}`);
          }
        }
      } catch (fileError) {
        console.log('No templates.json file found or file is empty');
      }
    } catch (templatesError) {
      errors.push(`Error migrating templates: ${templatesError}`);
    }

    const success = errors.length === 0 || (usersCount > 0 || templatesCount > 0);
    const message = success 
      ? `Migration completed successfully. Users: ${usersCount}, Templates: ${templatesCount}`
      : 'Migration failed with errors';

    return {
      success,
      message,
      details: {
        usersmigrated: usersCount,
        templatesMigrated: templatesCount,
        errors
      }
    };

  } catch (error) {
    return {
      success: false,
      message: `Migration failed: ${error}`,
      details: {
        usersmigrated: usersCount,
        templatesMigrated: templatesCount,
        errors: [...errors, `General error: ${error}`]
      }
    };
  }
}

// Backup existing JSON files before migration
export async function backupJsonFiles(): Promise<void> {
  const backupDir = path.join(process.cwd(), 'data', 'backup');
  
  try {
    await fs.mkdir(backupDir, { recursive: true });
  } catch {
    // Directory already exists
  }

  // Backup users.json
  try {
    const usersPath = path.join(process.cwd(), 'data', 'users.json');
    const backupUsersPath = path.join(backupDir, `users_backup_${Date.now()}.json`);
    await fs.copyFile(usersPath, backupUsersPath);
    console.log(`Backed up users.json to ${backupUsersPath}`);
  } catch {
    console.log('No users.json file to backup');
  }

  // Backup templates.json
  try {
    const templatesPath = path.join(process.cwd(), 'public', 'templates', 'templates.json');
    const backupTemplatesPath = path.join(backupDir, `templates_backup_${Date.now()}.json`);
    await fs.copyFile(templatesPath, backupTemplatesPath);
    console.log(`Backed up templates.json to ${backupTemplatesPath}`);
  } catch {
    console.log('No templates.json file to backup');
  }
}

// Verify migration by comparing data
export async function verifyMigration(): Promise<{
  success: boolean;
  message: string;
  details: {
    usersMatch: boolean;
    templatesMatch: boolean;
    issues: string[];
  };
}> {
  const issues: string[] = [];
  let usersMatch = true;
  let templatesMatch = true;

  try {
    // Verify users
    try {
      const usersFilePath = path.join(process.cwd(), 'data', 'users.json');
      const usersData = await fs.readFile(usersFilePath, 'utf-8');
      
      let jsonUsers: any[] = [];
      try {
        jsonUsers = JSON.parse(usersData);
      } catch {
        const { decrypt } = await import('./models/user');
        const decryptedData = decrypt(usersData);
        jsonUsers = JSON.parse(decryptedData);
      }

      const dbUsers = await UserModel.getAllUsers();
      
      if (jsonUsers.length !== dbUsers.length) {
        usersMatch = false;
        issues.push(`User count mismatch: JSON has ${jsonUsers.length}, DB has ${dbUsers.length}`);
      }

      for (const jsonUser of jsonUsers) {
        const dbUser = dbUsers.find(u => u.username === jsonUser.username);
        if (!dbUser) {
          usersMatch = false;
          issues.push(`User ${jsonUser.username} not found in database`);
        }
      }
    } catch {
      console.log('No users.json file to verify against');
    }

    // Verify templates
    try {
      const templatesFilePath = path.join(process.cwd(), 'public', 'templates', 'templates.json');
      const templatesData = await fs.readFile(templatesFilePath, 'utf-8');
      const jsonTemplates: Template[] = JSON.parse(templatesData);

      const dbTemplates = await TemplateModel.findAll();
      
      if (jsonTemplates.length !== dbTemplates.length) {
        templatesMatch = false;
        issues.push(`Template count mismatch: JSON has ${jsonTemplates.length}, DB has ${dbTemplates.length}`);
      }

      for (const jsonTemplate of jsonTemplates) {
        const dbTemplate = dbTemplates.find(t => t.id === jsonTemplate.id);
        if (!dbTemplate) {
          templatesMatch = false;
          issues.push(`Template ${jsonTemplate.name} not found in database`);
        }
      }
    } catch {
      console.log('No templates.json file to verify against');
    }

    const success = usersMatch && templatesMatch && issues.length === 0;
    const message = success 
      ? 'Migration verification successful - all data matches'
      : 'Migration verification found issues';

    return {
      success,
      message,
      details: {
        usersMatch,
        templatesMatch,
        issues
      }
    };

  } catch (error) {
    return {
      success: false,
      message: `Verification failed: ${error}`,
      details: {
        usersMatch: false,
        templatesMatch: false,
        issues: [`Verification error: ${error}`]
      }
    };
  }
}
