"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Shield,
  User,
  Lock,
  Key,
  HelpCircle,
  Eye,
  EyeOff,
  Copy,
  Check,
} from "lucide-react";
import { toast } from "sonner";
import { SECURITY_QUESTIONS } from "@/lib/auth";

interface AdminAuthDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAuthSuccess: () => void;
}

type AuthMode = "login" | "signup" | "forgot";
type RecoveryMethod = "privateKey" | "securityQuestions";

export function AdminAuthDialog({
  open,
  onOpenChange,
  onAuthSuccess,
}: AdminAuthDialogProps) {
  const [authMode, setAuthMode] = useState<AuthMode>("login");
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [generatedPrivateKey, setGeneratedPrivateKey] = useState<string>("");
  const [copiedPrivateKey, setCopiedPrivateKey] = useState(false);

  // Form states
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [recoveryMethod, setRecoveryMethod] =
    useState<RecoveryMethod>("privateKey");
  const [privateKey, setPrivateKey] = useState("");
  const [securityQuestions, setSecurityQuestions] = useState<
    Array<{ question: string; answer: string }>
  >([]);
  const [securityAnswers, setSecurityAnswers] = useState<string[]>([]);
  const [userSecurityQuestions, setUserSecurityQuestions] = useState<string[]>(
    []
  );
  const [hasPrivateKey, setHasPrivateKey] = useState(false);

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setAuthMode("login");
      setUsername("");
      setPassword("");
      setConfirmPassword("");
      setPrivateKey("");
      setSecurityQuestions([]);
      setSecurityAnswers([]);
      setUserSecurityQuestions([]);
      setGeneratedPrivateKey("");
      setCopiedPrivateKey(false);
      setShowPassword(false);
    }
  }, [open]);

  // Initialize security questions for signup
  useEffect(() => {
    if (authMode === "signup" && recoveryMethod === "securityQuestions") {
      setSecurityQuestions(
        SECURITY_QUESTIONS.slice(0, 3).map((q) => ({ question: q, answer: "" }))
      );
    }
  }, [authMode, recoveryMethod]);

  const handleLogin = async () => {
    if (!username || !password) {
      toast.error("Please fill in all fields");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ username, password }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success("Login successful!");
        onAuthSuccess();
        onOpenChange(false);
      } else {
        toast.error(data.message || "Login failed");
      }
    } catch (error) {
      toast.error("Login failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleSignup = async () => {
    if (!username || !password || !confirmPassword) {
      toast.error("Please fill in all fields");
      return;
    }

    if (password !== confirmPassword) {
      toast.error("Passwords do not match");
      return;
    }

    if (password.length < 6) {
      toast.error("Password must be at least 6 characters long");
      return;
    }

    if (recoveryMethod === "securityQuestions") {
      const unansweredQuestions = securityQuestions.filter(
        (sq) => !sq.answer.trim()
      );
      if (unansweredQuestions.length > 0) {
        toast.error("Please answer all security questions");
        return;
      }
    }

    setLoading(true);
    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username,
          password,
          recoveryMethod,
          privateKey: recoveryMethod === "privateKey" ? privateKey : undefined,
          securityQuestions:
            recoveryMethod === "securityQuestions"
              ? securityQuestions
              : undefined,
        }),
      });

      const data = await response.json();

      if (data.success) {
        if (data.privateKey) {
          setGeneratedPrivateKey(data.privateKey);
          toast.success("Account created! Please save your private key.");
        } else {
          toast.success("Account created successfully!");
          setAuthMode("login");
        }
      } else {
        toast.error(data.message || "Signup failed");
      }
    } catch (error) {
      toast.error("Signup failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleForgotPassword = async () => {
    if (!username) {
      toast.error("Please enter your username");
      return;
    }

    // First, get user's recovery options
    try {
      const response = await fetch(
        `/api/auth/recover?username=${encodeURIComponent(username)}`
      );
      const data = await response.json();

      if (data.success) {
        setUserSecurityQuestions(data.questions);
        setHasPrivateKey(data.hasPrivateKey);
        setSecurityAnswers(new Array(data.questions.length).fill(""));
      } else {
        toast.error(data.message || "User not found");
        return;
      }
    } catch (error) {
      toast.error("Failed to get recovery information");
      return;
    }
  };

  const handleRecoverAccount = async () => {
    if (!username) {
      toast.error("Please enter your username");
      return;
    }

    const isUsingPrivateKey = recoveryMethod === "privateKey";

    if (isUsingPrivateKey && !privateKey) {
      toast.error("Please enter your private key");
      return;
    }

    if (
      !isUsingPrivateKey &&
      securityAnswers.some((answer) => !answer.trim())
    ) {
      toast.error("Please answer all security questions");
      return;
    }

    setLoading(true);
    try {
      const response = await fetch("/api/auth/recover", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          username,
          recoveryMethod,
          privateKey: isUsingPrivateKey ? privateKey : undefined,
          securityAnswers: !isUsingPrivateKey ? securityAnswers : undefined,
        }),
      });

      const data = await response.json();

      if (data.success) {
        toast.success(
          `Account recovered! Your new password is: ${data.newPassword}`
        );
        setAuthMode("login");
        setPassword(data.newPassword);
      } else {
        toast.error(data.message || "Account recovery failed");
      }
    } catch (error) {
      toast.error("Account recovery failed. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const copyPrivateKey = async () => {
    try {
      await navigator.clipboard.writeText(generatedPrivateKey);
      setCopiedPrivateKey(true);
      toast.success("Private key copied to clipboard!");
      setTimeout(() => setCopiedPrivateKey(false), 2000);
    } catch (error) {
      toast.error("Failed to copy private key");
    }
  };

  const renderLoginForm = () => (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="username">Username</Label>
        <Input
          id="username"
          type="text"
          placeholder="Enter your username"
          value={username}
          onChange={(e) => setUsername(e.target.value)}
          disabled={loading}
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="password">Password</Label>
        <div className="relative">
          <Input
            id="password"
            type={showPassword ? "text" : "password"}
            placeholder="Enter your password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            disabled={loading}
          />
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? (
              <EyeOff className="h-4 w-4" />
            ) : (
              <Eye className="h-4 w-4" />
            )}
          </Button>
        </div>
      </div>
      <div className="flex flex-col space-y-2">
        <Button onClick={handleLogin} disabled={loading} className="w-full">
          {loading ? "Logging in..." : "Login"}
        </Button>
        <div className="flex justify-between text-sm">
          <Button
            variant="link"
            className="p-0 h-auto"
            onClick={() => setAuthMode("signup")}
          >
            Create account
          </Button>
          <Button
            variant="link"
            className="p-0 h-auto"
            onClick={() => setAuthMode("forgot")}
          >
            Forgot password?
          </Button>
        </div>
      </div>
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Admin Authentication
          </DialogTitle>
          <DialogDescription>
            {authMode === "login" &&
              "Enter your credentials to access admin features"}
            {authMode === "signup" &&
              "Create an admin account to manage the system"}
            {authMode === "forgot" &&
              "Recover your account using your recovery method"}
          </DialogDescription>
        </DialogHeader>

        {generatedPrivateKey ? (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-lg">
                <Key className="h-5 w-5" />
                Save Your Private Key
              </CardTitle>
              <CardDescription>
                This is your private key for account recovery. Save it in a
                secure location!
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="p-3 bg-muted rounded-md font-mono text-sm break-all">
                {generatedPrivateKey}
              </div>
              <div className="flex gap-2">
                <Button
                  onClick={copyPrivateKey}
                  variant="outline"
                  className="flex-1"
                >
                  {copiedPrivateKey ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                  {copiedPrivateKey ? "Copied!" : "Copy Key"}
                </Button>
                <Button
                  onClick={() => {
                    setGeneratedPrivateKey("");
                    setAuthMode("login");
                  }}
                  className="flex-1"
                >
                  Continue to Login
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <>
            {authMode === "login" && renderLoginForm()}

            {authMode === "signup" && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="signup-username">Username</Label>
                  <Input
                    id="signup-username"
                    type="text"
                    placeholder="Choose a username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    disabled={loading}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="signup-password">Password</Label>
                  <div className="relative">
                    <Input
                      id="signup-password"
                      type={showPassword ? "text" : "password"}
                      placeholder="Choose a password (min 6 characters)"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      disabled={loading}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirm-password">Confirm Password</Label>
                  <Input
                    id="confirm-password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Confirm your password"
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    disabled={loading}
                  />
                </div>

                {/* Recovery Method Selection */}
                <div className="space-y-3">
                  <Label>Recovery Method</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Button
                      type="button"
                      variant={
                        recoveryMethod === "privateKey" ? "default" : "outline"
                      }
                      onClick={() => setRecoveryMethod("privateKey")}
                      className="flex items-center gap-2"
                    >
                      <Key className="h-4 w-4" />
                      Private Key
                    </Button>
                    <Button
                      type="button"
                      variant={
                        recoveryMethod === "securityQuestions"
                          ? "default"
                          : "outline"
                      }
                      onClick={() => setRecoveryMethod("securityQuestions")}
                      className="flex items-center gap-2"
                    >
                      <HelpCircle className="h-4 w-4" />
                      Questions
                    </Button>
                  </div>
                </div>

                {/* Private Key Input */}
                {recoveryMethod === "privateKey" && (
                  <div className="space-y-2">
                    <Label htmlFor="private-key">Private Key (Optional)</Label>
                    <Input
                      id="private-key"
                      type="text"
                      placeholder="Leave empty to generate automatically"
                      value={privateKey}
                      onChange={(e) => setPrivateKey(e.target.value)}
                      disabled={loading}
                    />
                    <p className="text-xs text-muted-foreground">
                      If left empty, a secure private key will be generated for
                      you.
                    </p>
                  </div>
                )}

                {/* Security Questions */}
                {recoveryMethod === "securityQuestions" && (
                  <div className="space-y-3">
                    <Label>Security Questions</Label>
                    {securityQuestions.map((sq, index) => (
                      <div key={index} className="space-y-2">
                        <Label className="text-sm font-medium">
                          {sq.question}
                        </Label>
                        <Input
                          type="text"
                          placeholder="Your answer"
                          value={sq.answer}
                          onChange={(e) => {
                            const newQuestions = [...securityQuestions];
                            newQuestions[index].answer = e.target.value;
                            setSecurityQuestions(newQuestions);
                          }}
                          disabled={loading}
                        />
                      </div>
                    ))}
                  </div>
                )}

                <div className="flex flex-col space-y-2">
                  <Button
                    onClick={handleSignup}
                    disabled={loading}
                    className="w-full"
                  >
                    {loading ? "Creating Account..." : "Create Account"}
                  </Button>
                  <Button
                    variant="link"
                    className="p-0 h-auto"
                    onClick={() => setAuthMode("login")}
                  >
                    Already have an account? Login
                  </Button>
                </div>
              </div>
            )}

            {authMode === "forgot" && (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="forgot-username">Username</Label>
                  <Input
                    id="forgot-username"
                    type="text"
                    placeholder="Enter your username"
                    value={username}
                    onChange={(e) => setUsername(e.target.value)}
                    disabled={loading}
                  />
                </div>

                {userSecurityQuestions.length === 0 && !hasPrivateKey ? (
                  <Button
                    onClick={handleForgotPassword}
                    disabled={loading}
                    className="w-full"
                  >
                    {loading ? "Checking..." : "Check Recovery Options"}
                  </Button>
                ) : (
                  <>
                    {/* Recovery Method Selection */}
                    <div className="space-y-3">
                      <Label>Choose Recovery Method</Label>
                      <div className="grid grid-cols-1 gap-2">
                        {hasPrivateKey && (
                          <Button
                            type="button"
                            variant={
                              recoveryMethod === "privateKey"
                                ? "default"
                                : "outline"
                            }
                            onClick={() => setRecoveryMethod("privateKey")}
                            className="flex items-center gap-2"
                          >
                            <Key className="h-4 w-4" />
                            Use Private Key
                          </Button>
                        )}
                        {userSecurityQuestions.length > 0 && (
                          <Button
                            type="button"
                            variant={
                              recoveryMethod === "securityQuestions"
                                ? "default"
                                : "outline"
                            }
                            onClick={() =>
                              setRecoveryMethod("securityQuestions")
                            }
                            className="flex items-center gap-2"
                          >
                            <HelpCircle className="h-4 w-4" />
                            Answer Security Questions
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Private Key Recovery */}
                    {recoveryMethod === "privateKey" && (
                      <div className="space-y-2">
                        <Label htmlFor="recovery-private-key">
                          Private Key
                        </Label>
                        <Input
                          id="recovery-private-key"
                          type="text"
                          placeholder="Enter your private key"
                          value={privateKey}
                          onChange={(e) => setPrivateKey(e.target.value)}
                          disabled={loading}
                        />
                      </div>
                    )}

                    {/* Security Questions Recovery */}
                    {recoveryMethod === "securityQuestions" &&
                      userSecurityQuestions.length > 0 && (
                        <div className="space-y-3">
                          <Label>Answer Your Security Questions</Label>
                          {userSecurityQuestions.map((question, index) => (
                            <div key={index} className="space-y-2">
                              <Label className="text-sm font-medium">
                                {question}
                              </Label>
                              <Input
                                type="text"
                                placeholder="Your answer"
                                value={securityAnswers[index] || ""}
                                onChange={(e) => {
                                  const newAnswers = [...securityAnswers];
                                  newAnswers[index] = e.target.value;
                                  setSecurityAnswers(newAnswers);
                                }}
                                disabled={loading}
                              />
                            </div>
                          ))}
                        </div>
                      )}

                    <div className="flex flex-col space-y-2">
                      <Button
                        onClick={handleRecoverAccount}
                        disabled={loading}
                        className="w-full"
                      >
                        {loading ? "Recovering Account..." : "Recover Account"}
                      </Button>
                      <Button
                        variant="link"
                        className="p-0 h-auto"
                        onClick={() => {
                          setAuthMode("login");
                          setUserSecurityQuestions([]);
                          setHasPrivateKey(false);
                        }}
                      >
                        Back to Login
                      </Button>
                    </div>
                  </>
                )}
              </div>
            )}
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
