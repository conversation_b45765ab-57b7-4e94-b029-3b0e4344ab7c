// Migration script to move data from JSON files to SQLite
import { migrateToSQLite, backupJsonFiles, verifyMigration } from '../src/lib/migrate-to-sqlite';
import { initDatabase, runMigrations } from '../src/lib/database';

async function runMigration() {
  try {
    console.log('🚀 Starting migration to SQLite...\n');
    
    // Step 1: Initialize database
    console.log('1. Initializing database...');
    await initDatabase();
    await runMigrations();
    console.log('✓ Database initialized successfully\n');
    
    // Step 2: Backup JSON files
    console.log('2. Backing up existing JSON files...');
    await backupJsonFiles();
    console.log('✓ JSON files backed up successfully\n');
    
    // Step 3: Migrate data
    console.log('3. Migrating data from JSON to SQLite...');
    const migrationResult = await migrateToSQLite();
    
    if (migrationResult.success) {
      console.log('✓ Migration completed successfully');
      console.log(`   - Users migrated: ${migrationResult.details.usersmigrated}`);
      console.log(`   - Templates migrated: ${migrationResult.details.templatesMigrated}`);
    } else {
      console.log('❌ Migration failed:', migrationResult.message);
      if (migrationResult.details.errors.length > 0) {
        console.log('   Errors:');
        migrationResult.details.errors.forEach(error => {
          console.log(`   - ${error}`);
        });
      }
    }
    console.log('');
    
    // Step 4: Verify migration
    console.log('4. Verifying migration...');
    const verificationResult = await verifyMigration();
    
    if (verificationResult.success) {
      console.log('✓ Migration verification successful');
      console.log('   - All data matches between JSON and SQLite');
    } else {
      console.log('❌ Migration verification failed:', verificationResult.message);
      if (verificationResult.details.issues.length > 0) {
        console.log('   Issues found:');
        verificationResult.details.issues.forEach(issue => {
          console.log(`   - ${issue}`);
        });
      }
    }
    console.log('');
    
    // Final summary
    if (migrationResult.success && verificationResult.success) {
      console.log('🎉 Migration completed successfully!');
      console.log('');
      console.log('Your LDIS system is now using SQLite for data storage.');
      console.log('The original JSON files have been backed up in the data/backup directory.');
      console.log('');
      console.log('Benefits of SQLite:');
      console.log('- Better performance for large datasets');
      console.log('- ACID transactions for data integrity');
      console.log('- Concurrent access support');
      console.log('- Built-in indexing for faster queries');
      console.log('- Document generation tracking');
    } else {
      console.log('⚠️  Migration completed with issues.');
      console.log('Please review the errors above and consider running the migration again.');
    }
    
  } catch (error) {
    console.error('❌ Migration failed with error:', error);
    process.exit(1);
  }
}

// Run the migration
runMigration();
