import { initDatabase, runMigrations } from './database';

let isInitialized = false;

export async function ensureDatabaseInitialized(): Promise<void> {
  if (isInitialized) {
    return;
  }

  try {
    console.log('Initializing SQLite database...');
    await initDatabase();
    await runMigrations();
    isInitialized = true;
    console.log('SQLite database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    // Don't throw error to prevent app from crashing
    // The app can still work with JSON files as fallback
  }
}

// Auto-initialize on import in development
if (process.env.NODE_ENV === 'development') {
  ensureDatabaseInitialized().catch(console.error);
}
