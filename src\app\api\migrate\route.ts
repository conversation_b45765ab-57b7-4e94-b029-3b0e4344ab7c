import { NextRequest, NextResponse } from 'next/server';
import { migrateToSQLite, backupJsonFiles, verifyMigration } from '@/lib/migrate-to-sqlite';
import { initDatabase, runMigrations } from '@/lib/database';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action } = body;

    switch (action) {
      case 'init':
        // Initialize database and run migrations
        await initDatabase();
        await runMigrations();
        return NextResponse.json({
          success: true,
          message: 'Database initialized successfully'
        });

      case 'backup':
        // Backup existing JSON files
        await backupJsonFiles();
        return NextResponse.json({
          success: true,
          message: 'JSON files backed up successfully'
        });

      case 'migrate':
        // Perform the migration
        const migrationResult = await migrateToSQLite();
        return NextResponse.json(migrationResult);

      case 'verify':
        // Verify the migration
        const verificationResult = await verifyMigration();
        return NextResponse.json(verificationResult);

      case 'full':
        // Perform full migration process
        try {
          // Step 1: Initialize database
          await initDatabase();
          await runMigrations();

          // Step 2: Backup JSON files
          await backupJsonFiles();

          // Step 3: Migrate data
          const fullMigrationResult = await migrateToSQLite();

          // Step 4: Verify migration
          const fullVerificationResult = await verifyMigration();

          return NextResponse.json({
            success: fullMigrationResult.success && fullVerificationResult.success,
            message: 'Full migration completed',
            migration: fullMigrationResult,
            verification: fullVerificationResult
          });
        } catch (error) {
          return NextResponse.json({
            success: false,
            message: `Full migration failed: ${error}`,
            error: error instanceof Error ? error.message : String(error)
          }, { status: 500 });
        }

      default:
        return NextResponse.json({
          success: false,
          message: 'Invalid action. Use: init, backup, migrate, verify, or full'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Migration API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Migration operation failed',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}

export async function GET() {
  try {
    // Return migration status and database info
    const { getDatabase } = await import('@/lib/database');
    const { TemplateModel } = await import('@/lib/models/template');
    const { UserModel } = await import('@/lib/models/user');

    const db = await getDatabase();
    
    // Get counts from database
    const templateCount = await TemplateModel.count();
    const userCount = (await UserModel.getAllUsers()).length;

    // Check if database file exists
    const path = await import('path');
    const fs = await import('fs/promises');
    const dbPath = path.join(process.cwd(), 'data', 'ldis.db');
    
    let dbExists = false;
    let dbSize = 0;
    
    try {
      const stats = await fs.stat(dbPath);
      dbExists = true;
      dbSize = stats.size;
    } catch {
      dbExists = false;
    }

    return NextResponse.json({
      success: true,
      database: {
        exists: dbExists,
        size: dbSize,
        path: dbPath
      },
      counts: {
        users: userCount,
        templates: templateCount
      },
      status: dbExists ? 'initialized' : 'not_initialized'
    });
  } catch (error) {
    console.error('Migration status error:', error);
    return NextResponse.json({
      success: false,
      message: 'Failed to get migration status',
      error: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
