// Simple test script to verify SQLite integration
import { initDatabase, getDatabase } from '../src/lib/database';
import { UserModel } from '../src/lib/models/user';
import { TemplateModel } from '../src/lib/models/template';

async function testSQLite() {
  try {
    console.log('Testing SQLite integration...');
    
    // Initialize database
    console.log('Initializing database...');
    await initDatabase();
    console.log('✓ Database initialized successfully');
    
    // Get database instance
    const db = await getDatabase();
    console.log('✓ Database connection established');
    
    // Test basic operations
    console.log('Testing basic database operations...');
    
    // Check if tables exist
    const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table'").all();
    console.log('✓ Tables found:', tables.map((t: any) => t.name));
    
    // Test user operations
    console.log('Testing user operations...');
    const testUser = {
      username: 'test_user_' + Date.now(),
      passwordHash: 'test_hash',
      recoveryOptions: { privateKey: 'test_key' }
    };
    
    const createdUser = await UserModel.create(testUser);
    console.log('✓ User created:', createdUser.username);
    
    // Find the user
    const foundUser = await UserModel.findByUsername(testUser.username);
    console.log('✓ User found:', foundUser ? foundUser.username : 'not found');
    
    // Test template operations
    console.log('Testing template operations...');
    const testTemplate = {
      id: 'test_template_' + Date.now(),
      name: 'Test Template',
      description: 'A test template',
      filename: 'test.html',
      placeholders: ['[NAME]', '[DATE]'],
      layoutSize: 'A4' as const,
      hasApplicantPhoto: false
    };
    
    const createdTemplate = await TemplateModel.create(testTemplate);
    console.log('✓ Template created:', createdTemplate.name);
    
    // Find the template
    const foundTemplate = await TemplateModel.findById(testTemplate.id);
    console.log('✓ Template found:', foundTemplate ? foundTemplate.name : 'not found');
    
    // Get all templates
    const allTemplates = await TemplateModel.findAll();
    console.log('✓ Total templates:', allTemplates.length);
    
    // Clean up test data
    await UserModel.deleteUser(testUser.username);
    await TemplateModel.delete(testTemplate.id);
    console.log('✓ Test data cleaned up');
    
    console.log('\n🎉 SQLite integration test completed successfully!');
    
  } catch (error) {
    console.error('❌ SQLite integration test failed:', error);
    process.exit(1);
  }
}

// Run the test
testSQLite();
