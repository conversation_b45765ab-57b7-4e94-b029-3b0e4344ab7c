import CryptoJS from 'crypto-js';
import { promises as fs } from 'fs';
import path from 'path';

// Secret key for encryption (in production, this should be from environment variables)
const SECRET_KEY = 'LDIS_AUTH_SECRET_2024';

// File paths
const USERS_DIR = path.join(process.cwd(), 'data');
const USERS_FILE_PATH = path.join(USERS_DIR, 'users.json');

export interface User {
  username: string;
  passwordHash: string;
  recoveryOptions: {
    privateKey?: string;
    securityQuestions?: {
      question: string;
      answerHash: string;
    }[];
  };
  createdAt: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export interface SignupData {
  username: string;
  password: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityQuestions?: {
    question: string;
    answer: string;
  }[];
}

export interface RecoveryData {
  username: string;
  recoveryMethod: 'privateKey' | 'securityQuestions';
  privateKey?: string;
  securityAnswers?: string[];
}

// Security questions
export const SECURITY_QUESTIONS = [
  "What was the name of your childhood best friend?",
  "What is the name of the street you grew up on?",
  "What was the name of your first pet?",
  "What was your favorite subject in school?",
  "What is the middle name of your oldest sibling?"
];

// Encryption utilities
export const encrypt = (text: string): string => {
  return CryptoJS.AES.encrypt(text, SECRET_KEY).toString();
};

export const decrypt = (ciphertext: string): string => {
  const bytes = CryptoJS.AES.decrypt(ciphertext, SECRET_KEY);
  return bytes.toString(CryptoJS.enc.Utf8);
};

// Hash password
export const hashPassword = (password: string): string => {
  return CryptoJS.SHA256(password + SECRET_KEY).toString();
};

// Generate private key
export const generatePrivateKey = (): string => {
  return CryptoJS.lib.WordArray.random(32).toString();
};

// Ensure data directory exists
const ensureDataDir = async (): Promise<void> => {
  try {
    await fs.access(USERS_DIR);
  } catch {
    await fs.mkdir(USERS_DIR, { recursive: true });
  }
};

// Storage utilities
export const getStoredUsers = async (): Promise<User[]> => {
  try {
    await ensureDataDir();
    const encryptedData = await fs.readFile(USERS_FILE_PATH, 'utf-8');
    const decryptedData = decrypt(encryptedData);
    return JSON.parse(decryptedData);
  } catch (error) {
    // File doesn't exist or is empty, return empty array
    if ((error as any).code === 'ENOENT') {
      return [];
    }
    console.error('Error loading users:', error);
    return [];
  }
};

export const saveUsers = async (users: User[]): Promise<void> => {
  try {
    await ensureDataDir();
    const encryptedData = encrypt(JSON.stringify(users, null, 2));
    await fs.writeFile(USERS_FILE_PATH, encryptedData, 'utf-8');
  } catch (error) {
    console.error('Error saving users:', error);
    throw error;
  }
};

// Authentication functions
export const signup = async (signupData: SignupData): Promise<{ success: boolean; message: string; privateKey?: string }> => {
  try {
    const users = await getStoredUsers();

    // Check if username already exists
    if (users.find(user => user.username === signupData.username)) {
      return { success: false, message: 'Username already exists' };
    }

    // Create new user
    const newUser: User = {
      username: signupData.username,
      passwordHash: hashPassword(signupData.password),
      recoveryOptions: {},
      createdAt: new Date().toISOString()
    };

    // Set recovery options
    if (signupData.recoveryMethod === 'privateKey') {
      const privateKey = signupData.privateKey || generatePrivateKey();
      newUser.recoveryOptions.privateKey = encrypt(privateKey);

      users.push(newUser);
      await saveUsers(users);

      return {
        success: true,
        message: 'Account created successfully',
        privateKey: privateKey
      };
    } else if (signupData.recoveryMethod === 'securityQuestions' && signupData.securityQuestions) {
      newUser.recoveryOptions.securityQuestions = signupData.securityQuestions.map(sq => ({
        question: sq.question,
        answerHash: hashPassword(sq.answer.toLowerCase().trim())
      }));

      users.push(newUser);
      await saveUsers(users);

      return { success: true, message: 'Account created successfully' };
    }

    return { success: false, message: 'Invalid recovery method' };
  } catch (error) {
    console.error('Signup error:', error);
    return { success: false, message: 'An error occurred during signup' };
  }
};

export const login = async (credentials: LoginCredentials): Promise<{ success: boolean; message: string }> => {
  try {
    const users = await getStoredUsers();
    const user = users.find(u => u.username === credentials.username);

    if (!user) {
      return { success: false, message: 'Invalid username or password' };
    }

    const passwordHash = hashPassword(credentials.password);
    if (user.passwordHash !== passwordHash) {
      return { success: false, message: 'Invalid username or password' };
    }

    // Set admin mode
    localStorage.setItem('adminMode', 'true');
    localStorage.setItem('currentUser', credentials.username);
    window.dispatchEvent(new Event('adminModeChanged'));

    return { success: true, message: 'Login successful' };
  } catch (error) {
    console.error('Login error:', error);
    return { success: false, message: 'An error occurred during login' };
  }
};

export const recoverPassword = async (recoveryData: RecoveryData, newPassword: string): Promise<{ success: boolean; message: string }> => {
  try {
    const users = await getStoredUsers();
    const userIndex = users.findIndex(u => u.username === recoveryData.username);

    if (userIndex === -1) {
      return { success: false, message: 'User not found' };
    }

    const user = users[userIndex];

    // Verify recovery method
    if (recoveryData.recoveryMethod === 'privateKey') {
      if (!user.recoveryOptions.privateKey || !recoveryData.privateKey) {
        return { success: false, message: 'Invalid recovery method' };
      }

      const storedPrivateKey = decrypt(user.recoveryOptions.privateKey);
      if (storedPrivateKey !== recoveryData.privateKey) {
        return { success: false, message: 'Invalid private key' };
      }
    } else if (recoveryData.recoveryMethod === 'securityQuestions') {
      if (!user.recoveryOptions.securityQuestions || !recoveryData.securityAnswers) {
        return { success: false, message: 'Invalid recovery method' };
      }

      // Verify all security answers
      for (let i = 0; i < user.recoveryOptions.securityQuestions.length; i++) {
        const expectedHash = user.recoveryOptions.securityQuestions[i].answerHash;
        const providedHash = hashPassword(recoveryData.securityAnswers[i].toLowerCase().trim());

        if (expectedHash !== providedHash) {
          return { success: false, message: 'Incorrect security answers' };
        }
      }
    }

    // Update password
    users[userIndex].passwordHash = hashPassword(newPassword);
    await saveUsers(users);

    return { success: true, message: 'Password reset successfully' };
  } catch (error) {
    console.error('Password recovery error:', error);
    return { success: false, message: 'An error occurred during password recovery' };
  }
};

export const logout = (): void => {
  localStorage.setItem('adminMode', 'false');
  localStorage.removeItem('currentUser');
  window.dispatchEvent(new Event('adminModeChanged'));
};

export const getCurrentUser = (): string | null => {
  return localStorage.getItem('currentUser');
};

export const getUserRecoveryOptions = async (username: string): Promise<{ hasPrivateKey: boolean; securityQuestions: string[] }> => {
  try {
    const users = await getStoredUsers();
    const user = users.find(u => u.username === username);

    if (!user) {
      return { hasPrivateKey: false, securityQuestions: [] };
    }

    return {
      hasPrivateKey: !!user.recoveryOptions.privateKey,
      securityQuestions: user.recoveryOptions.securityQuestions?.map(sq => sq.question) || []
    };
  } catch (error) {
    console.error('Error getting user recovery options:', error);
    return { hasPrivateKey: false, securityQuestions: [] };
  }
};
